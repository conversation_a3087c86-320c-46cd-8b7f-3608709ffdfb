'use client'

import { useState, useEffect } from 'react'
import { BarChart3, History, Calendar, Settings, ChevronLeft, ChevronRight } from 'lucide-react'
import { useTheme } from 'next-themes'

interface SidebarProps {
  activeSection: string
  setActiveSection: (section: string) => void
}

export default function Sidebar({ activeSection, setActiveSection }: SidebarProps) {
  const { resolvedTheme } = useTheme()
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
    const savedState = localStorage.getItem('sidebar-collapsed')
    if (savedState !== null) {
      setIsCollapsed(JSON.parse(savedState))
    }
  }, [])

  useEffect(() => {
    if (mounted) {
      localStorage.setItem('sidebar-collapsed', JSON.stringify(isCollapsed))
    }
  }, [isCollapsed, mounted])

  const toggleSidebar = () => {
    setIsCollapsed(!isCollapsed)
  }

  const menuItems = [
    {
      id: 'api-graphing',
      label: 'API Graphing & Visuals',
      icon: BarChart3
    },
    {
      id: 'history',
      label: 'History',
      icon: History
    },
    {
      id: 'calendar',
      label: 'Calendar',
      icon: Calendar
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: Settings
    },
  ]

  return (
    <div
      className={`shadow-xl border-r sticky top-16 h-[calc(100vh-4rem)] overflow-hidden transition-all duration-300 ease-in-out ${
        isCollapsed ? 'w-20 min-w-20' : 'w-80 min-w-80'
      }`}
      style={{
        backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
        borderColor: resolvedTheme === 'dark' ? '#334155' : '#e5e7eb',
        borderWidth: '1px',
        boxShadow: resolvedTheme === 'dark'
          ? '0 25px 50px -12px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(255, 255, 255, 0.05)'
          : '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(0, 0, 0, 0.05)'
      }}
    >
      <div className="flex flex-col h-full">
        <div
          className={`sticky top-0 z-20 transition-all duration-300 backdrop-blur-md ${
            isCollapsed ? 'px-3 py-3' : 'px-6 py-4'
          }`}
          style={{
            background: resolvedTheme === 'dark'
              ? 'linear-gradient(135deg, rgba(30, 41, 59, 0.98) 0%, rgba(51, 65, 85, 0.95) 100%)'
              : 'linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(249, 250, 251, 0.95) 100%)',
            borderBottom: resolvedTheme === 'dark'
              ? '1px solid rgba(148, 163, 184, 0.2)'
              : '1px solid rgba(229, 231, 235, 0.8)',
            boxShadow: resolvedTheme === 'dark'
              ? '0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2)'
              : '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
          }}
        >
          <div className={`flex items-center ${isCollapsed ? 'justify-center' : 'mb-3'}`}>
            <div
              className={`rounded-lg flex items-center justify-center transition-all duration-300 ${
                isCollapsed ? 'w-10 h-10' : 'w-8 h-8 mr-3'
              }`}
              style={{
                background: resolvedTheme === 'dark'
                  ? 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)'
                  : 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
                boxShadow: '0 4px 8px rgba(34, 197, 94, 0.3)'
              }}
            >
              <span className={`text-white font-bold ${isCollapsed ? 'text-base' : 'text-sm'}`}>⚡</span>
            </div>
            {!isCollapsed && (
              <h2
                className="text-lg font-bold transition-all duration-300 crisp-text"
                style={{
                  color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827',
                  textShadow: resolvedTheme === 'dark' ? '0 1px 2px rgba(0, 0, 0, 0.3)' : 'none'
                }}
              >
                Additional Tools
              </h2>
            )}
          </div>
          {!isCollapsed && (
            <p
              className="text-xs font-medium transition-all duration-300 crisp-text"
              style={{
                color: resolvedTheme === 'dark' ? '#94a3b8' : '#64748b',
                letterSpacing: '0.025em'
              }}
            >
              Advanced features and utilities
            </p>
          )}
        </div>

        <div className="flex-1 relative">
          <button
            onClick={toggleSidebar}
            className="absolute top-4 right-2 z-30 p-2 rounded-lg transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 group"
            style={{
              backgroundColor: resolvedTheme === 'dark' 
                ? 'rgba(34, 197, 94, 0.1)' 
                : 'rgba(34, 197, 94, 0.08)',
              border: resolvedTheme === 'dark' 
                ? '1px solid rgba(34, 197, 94, 0.3)' 
                : '1px solid rgba(34, 197, 94, 0.2)',
              boxShadow: resolvedTheme === 'dark'
                ? '0 2px 8px rgba(34, 197, 94, 0.2)'
                : '0 2px 8px rgba(34, 197, 94, 0.15)'
            }}
            title={isCollapsed ? 'Expand Sidebar' : 'Collapse Sidebar'}
          >
            {isCollapsed ? (
              <ChevronRight 
                className="w-4 h-4 transition-all duration-200 group-hover:scale-105" 
                style={{ color: resolvedTheme === 'dark' ? '#4ade80' : '#16a34a' }}
              />
            ) : (
              <ChevronLeft 
                className="w-4 h-4 transition-all duration-200 group-hover:scale-105" 
                style={{ color: resolvedTheme === 'dark' ? '#4ade80' : '#16a34a' }}
              />
            )}
          </button>

          <div className="absolute inset-0 overflow-hidden">
            <nav className={`h-full pt-16 pb-6 overflow-y-auto sidebar-nav-scroll transition-all duration-300 space-y-1 ${
              isCollapsed ? 'px-2 pr-1' : 'px-4 pr-2'
            }`} style={{ marginRight: isCollapsed ? '-4px' : '-6px', paddingRight: isCollapsed ? '8px' : '10px' }}>
        {menuItems.map((item) => {
          const Icon = item.icon
          const isActive = activeSection === item.id

          return (
            <div key={item.id} className="relative group">
              <button
                onClick={() => setActiveSection(item.id)}
                className={`w-full flex items-center text-left transition-all duration-200 group sidebar-nav-item crisp-text relative overflow-hidden ${
                  isCollapsed
                    ? 'p-2.5 rounded-lg justify-center'
                    : 'p-3 rounded-xl'
                }`}
                style={{
                  background: isActive
                    ? (resolvedTheme === 'dark'
                      ? 'linear-gradient(135deg, rgba(34, 197, 94, 0.2) 0%, rgba(16, 185, 129, 0.15) 100%)'
                      : 'linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(16, 185, 129, 0.08) 100%)')
                    : 'transparent',
                  border: isActive
                    ? (resolvedTheme === 'dark' ? '1px solid rgba(34, 197, 94, 0.4)' : '1px solid rgba(34, 197, 94, 0.3)')
                    : '1px solid transparent',
                  boxShadow: isActive
                    ? (resolvedTheme === 'dark'
                      ? '0 4px 12px rgba(34, 197, 94, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
                      : '0 4px 12px rgba(34, 197, 94, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.8)')
                    : 'none'
                }}
                onMouseEnter={(e) => {
                  if (!isActive) {
                    e.currentTarget.style.background = resolvedTheme === 'dark'
                      ? 'rgba(71, 85, 105, 0.15)'
                      : 'rgba(243, 244, 246, 0.6)'
                    e.currentTarget.style.border = resolvedTheme === 'dark'
                      ? '1px solid rgba(148, 163, 184, 0.2)'
                      : '1px solid rgba(229, 231, 235, 0.6)'
                  }
                }}
                onMouseLeave={(e) => {
                  if (!isActive) {
                    e.currentTarget.style.background = 'transparent'
                    e.currentTarget.style.border = '1px solid transparent'
                  }
                }}
              >
                {isCollapsed ? (
                  <Icon
                    className="h-5 w-5 transition-all duration-200 relative z-10"
                    style={{
                      color: isActive
                        ? (resolvedTheme === 'dark' ? '#4ade80' : '#16a34a')
                        : (resolvedTheme === 'dark' ? '#e2e8f0' : '#64748b'),
                      filter: isActive ? 'drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2))' : 'none'
                    }}
                  />
                ) : (
                  <>
                    <div
                      className="transition-all duration-200 relative p-2 rounded-lg mr-3 overflow-hidden"
                      style={{
                        background: isActive
                          ? (resolvedTheme === 'dark'
                            ? 'linear-gradient(135deg, rgba(34, 197, 94, 0.3) 0%, rgba(16, 185, 129, 0.25) 100%)'
                            : 'linear-gradient(135deg, rgba(34, 197, 94, 0.2) 0%, rgba(16, 185, 129, 0.15) 100%)')
                          : (resolvedTheme === 'dark'
                            ? 'linear-gradient(135deg, rgba(71, 85, 105, 0.5) 0%, rgba(51, 65, 85, 0.4) 100%)'
                            : 'linear-gradient(135deg, rgba(243, 244, 246, 0.9) 0%, rgba(229, 231, 235, 0.7) 100%)'),
                        boxShadow: isActive
                          ? (resolvedTheme === 'dark'
                            ? '0 2px 8px rgba(34, 197, 94, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
                            : '0 2px 8px rgba(34, 197, 94, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.8)')
                          : (resolvedTheme === 'dark'
                            ? 'inset 0 1px 0 rgba(255, 255, 255, 0.05)'
                            : 'inset 0 1px 0 rgba(255, 255, 255, 0.9)')
                      }}
                    >
                      <Icon
                        className="h-4 w-4 transition-all duration-200 relative z-10"
                        style={{
                          color: isActive
                            ? (resolvedTheme === 'dark' ? '#4ade80' : '#16a34a')
                            : (resolvedTheme === 'dark' ? '#e2e8f0' : '#64748b'),
                          filter: isActive ? 'drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2))' : 'none'
                        }}
                      />
                    </div>
                    <div className="flex-1 sidebar-text">
                      <h3
                        className="font-medium text-sm transition-colors duration-200 leading-snug"
                        style={{
                          color: isActive
                            ? (resolvedTheme === 'dark' ? '#4ade80' : '#16a34a')
                            : (resolvedTheme === 'dark' ? '#f8fafc' : '#111827'),
                          fontWeight: isActive ? '600' : '500'
                        }}
                      >
                        {item.label}
                      </h3>
                    </div>
                  </>
                )}
              </button>
              
              {isCollapsed && (
                <div
                  className="absolute left-full ml-2 top-1/2 transform -translate-y-1/2 px-3 py-2 rounded-lg text-sm font-medium opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none z-50 whitespace-nowrap"
                  style={{
                    backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
                    color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827',
                    border: resolvedTheme === 'dark' ? '1px solid rgba(148, 163, 184, 0.3)' : '1px solid rgba(229, 231, 235, 0.8)',
                    boxShadow: resolvedTheme === 'dark'
                      ? '0 4px 12px rgba(0, 0, 0, 0.3)'
                      : '0 4px 12px rgba(0, 0, 0, 0.15)'
                  }}
                >
                  <div className="font-semibold">{item.label}</div>
                  <div
                    className="absolute right-full top-1/2 transform -translate-y-1/2 w-0 h-0"
                    style={{
                      borderTop: '6px solid transparent',
                      borderBottom: '6px solid transparent',
                      borderRight: `6px solid ${resolvedTheme === 'dark' ? '#1e293b' : '#ffffff'}`
                    }}
                  />
                </div>
              )}
            </div>
          )
        })}
            </nav>
          </div>
        </div>

        {/* Enhanced Sticky Footer Section */}
        <div
          className={`sticky bottom-0 z-20 transition-all duration-300 backdrop-blur-md ${
            isCollapsed ? 'px-3 py-3' : 'px-6 py-4'
          }`}
          style={{
            background: resolvedTheme === 'dark'
              ? 'linear-gradient(135deg, rgba(15, 23, 42, 0.98) 0%, rgba(30, 41, 59, 0.95) 100%)'
              : 'linear-gradient(135deg, rgba(249, 250, 251, 0.98) 0%, rgba(255, 255, 255, 0.95) 100%)',
            borderTop: resolvedTheme === 'dark'
              ? '1px solid rgba(148, 163, 184, 0.2)'
              : '1px solid rgba(229, 231, 235, 0.8)',
            boxShadow: resolvedTheme === 'dark'
              ? '0 -4px 6px -1px rgba(0, 0, 0, 0.3), 0 -2px 4px -1px rgba(0, 0, 0, 0.2)'
              : '0 -4px 6px -1px rgba(0, 0, 0, 0.1), 0 -2px 4px -1px rgba(0, 0, 0, 0.06)'
          }}
        >
          <div
            className="text-sm transition-colors duration-300"
            style={{
              color: resolvedTheme === 'dark' ? '#94a3b8' : '#64748b'
            }}
          >
            {isCollapsed ? (
              <div className="flex justify-center">
                <div
                  className="rounded-xl flex items-center justify-center relative overflow-hidden w-10 h-10"
                  style={{
                    background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
                    boxShadow: '0 4px 8px rgba(59, 130, 246, 0.3)'
                  }}
                >
                  <span className="text-white font-bold relative z-10 text-base">R</span>
                  <div
                    className="absolute inset-0 opacity-20"
                    style={{
                      background: 'linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.5) 50%, transparent 70%)'
                    }}
                  />
                </div>
              </div>
            ) : (
              <>
                <div className="flex items-center mb-2 space-x-3">
                  <div
                    className="rounded-xl flex items-center justify-center relative overflow-hidden w-8 h-8"
                    style={{
                      background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
                      boxShadow: '0 4px 8px rgba(59, 130, 246, 0.3)'
                    }}
                  >
                    <span className="text-white font-bold relative z-10 text-sm">R</span>
                    <div
                      className="absolute inset-0 opacity-20"
                      style={{
                        background: 'linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.5) 50%, transparent 70%)'
                      }}
                    />
                  </div>
                  <div>
                    <span
                      className="font-bold text-sm transition-colors duration-300 block"
                      style={{
                        color: resolvedTheme === 'dark' ? '#f8fafc' : '#1e293b',
                        textShadow: resolvedTheme === 'dark' ? '0 1px 2px rgba(0, 0, 0, 0.3)' : 'none'
                      }}
                    >
                      Revantad Store
                    </span>
                    <span
                      className="text-xs font-medium"
                      style={{
                        color: resolvedTheme === 'dark' ? '#64748b' : '#94a3b8',
                        letterSpacing: '0.025em'
                      }}
                    >
                      Professional Business Management
                    </span>
                  </div>
                </div>
                <div
                  className="text-xs font-medium px-3 py-2 rounded-lg"
                  style={{
                    backgroundColor: resolvedTheme === 'dark' ? 'rgba(71, 85, 105, 0.3)' : 'rgba(243, 244, 246, 0.8)',
                    color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280',
                    border: resolvedTheme === 'dark' ? '1px solid rgba(148, 163, 184, 0.2)' : '1px solid rgba(229, 231, 235, 0.6)'
                  }}
                >
                  Admin Dashboard v2.0
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
